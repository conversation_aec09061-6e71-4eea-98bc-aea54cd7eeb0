<?php

namespace App\Models;

use App\Events\TeamEventCreated;
use App\Events\TeamEventUpdated;
use App\Models\Scopes\LimitToCurrentUserAbsencesScope;
use App\Traits\HasScheduledNotifications;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventVoteReminderType;
use App\Types\EventReminderType;
use App\Types\NotificationType;
use App\Types\ReminderConfig;
use App\Types\TeamEventMatchType;
use App\Types\TeamEventType;
use App\Types\TeamEventVoteType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\URL;
use RRule\RRule;

/**
 * @property Team $team // cannot be null, added for phpstan to not expect null values
 */
class TeamEvent extends Model {
    use HasUuids;
    use HasScheduledNotifications;

    /**
     * Get the reminder configurations for this model
     * 
     * @return array<string, ReminderConfig>
     */
    protected static function getReminderConfigs(): array
    {
        return [
            'event_reminders' => ReminderConfig::eventReminder(
                'date_begin',
                'database',
                fn($model) => $model->team->getTimezone()
            ),
            'vote_reminders' => ReminderConfig::voteReminder(
                'created_at',
                'database', 
                fn($model) => $model->team->getTimezone()
            ),
        ];
    }

    protected $fillable = [
        'event_type',
        'series_type',
        'response_type',
        'title',
        'sub_text',
        'details',
        'details_manager',
        'date_begin',
        'time_begin',
        'time_end',
        'time_meet',
        'cancelled_at',
        'address_string',
        'match_type',
        'match_opponent_name',
        'vote_reminder_type',
        'vote_reminder_hours',
        'vote_reminder_sent',
        'event_reminder_type',
        'event_reminder_hours',
        'event_reminder_date',
        'event_reminder_sent',
    ];

    protected $casts = [
        'event_type'         => TeamEventType::class,
        'series_type'        => EventSeriesType::class,
        'response_type'      => EventResponseType::class,
        'date_begin'         => 'date',
        'cancelled_at'       => 'datetime',
        'match_type'         => TeamEventMatchType::class,
        'vote_reminder_type' => EventVoteReminderType::class,
        'vote_reminder_date' => 'datetime',
        'vote_reminder_sent' => 'boolean',
        'event_reminder_type' => EventReminderType::class,
        'event_reminder_date' => 'datetime',
        'event_reminder_sent' => 'boolean',
        // Virtual reminder attributes for new notification system
        'event_reminders'    => 'array',
        'vote_reminders'     => 'array',
    ];

    /**
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'created' => TeamEventCreated::class,
        'updated' => TeamEventUpdated::class,
    ];

    protected static function booted(): void {
        static::updating(function (TeamEvent $teamEvent) {
            // delete existing votes when team is changed
            if ($teamEvent->team_id !== $teamEvent->getOriginal('team_id')) {
                $teamEvent->votes()->delete();
            }
        });
        
        static::created(function (TeamEvent $teamEvent) {
            if ($teamEvent->isValidRecurringParent()) {
                $teamEvent->series->createRecurringEvents();
            }
        });
        
        static::saving(function (TeamEvent $teamEvent) {
            $modifiedVote = $teamEvent->calculateAndUpdateVoteReminderDate();
            $modifiedEvent = $teamEvent->calculateAndUpdateEventReminderDate();
            
            if ($modifiedVote) {
                $teamEvent->vote_reminder_sent = false;
            }
            if ($modifiedEvent) {
                $teamEvent->event_reminder_sent = false;
            }
        });
    }

    /**
     * Calculates the new vote_reminder_date based on the vote_reminder_type and vote_reminder_hours.
     * 
     * TODO(fabzo): Prevent vote_reminder_date from being set in the past or too far in the future?
     *              -- We actually don't care much. Could just be a validation in the UI. Won't break anything.
     */
    public function calculateAndUpdateVoteReminderDate(): bool {
        $previousValue = $this->vote_reminder_date?->copy();

        $timezone = $this->team->getTimezone();
        
        if ($this->vote_reminder_type == EventVoteReminderType::HOURS_AFTER_CREATION) {
            $createdAt = $this->created_at ?? Carbon::now('UTC');
            $this->vote_reminder_date = $createdAt->addHours($this->vote_reminder_hours);
        } elseif ($this->vote_reminder_type == EventVoteReminderType::HOURS_BEFORE_EVENT) {
            // Change the user supplied timezone to $team->getTimezone() using shiftTimezone
            // and then convert it to UTC using setTimezone.
            $this->vote_reminder_date = $this->dateTimeBegin()
                ->shiftTimezone($timezone)
                ->subHours($this->vote_reminder_hours)
                ->setTimezone('UTC');
        } else {
            $this->vote_reminder_date = null;
        }
        
        // TODO(fabzo): This isn't currently reflected in the UI.
        if ($this->isVoteReminderOutOfRange()) {
            $this->vote_reminder_type = EventVoteReminderType::NONE;
            $this->vote_reminder_date = null;
        }
        
        if ($this->isValidRecurringParent() && $this->vote_reminder_type == EventVoteReminderType::HOURS_AFTER_CREATION) {
            $this->vote_reminder_type = EventVoteReminderType::NONE;
            $this->vote_reminder_date = null;
        }
        
        if ($this->response_type == EventResponseType::AUTO_YES) {
            $this->vote_reminder_type = EventVoteReminderType::NONE;
            $this->vote_reminder_date = null;
        }
        
        return $this->vote_reminder_date != $previousValue;
    }

    public function calculateAndUpdateEventReminderDate(): bool {
        $previousValue = $this->event_reminder_date?->copy();

        if ($this->event_reminder_type == EventReminderType::HOURS_BEFORE_EVENT) {
            // Change the user supplied timezone to $team->getTimezone() using shiftTimezone
            // and then convert it to UTC using setTimezone.
            $this->event_reminder_date = $this->dateTimeBegin()
                ->shiftTimezone($this->team->getTimezone())
                ->subHours($this->event_reminder_hours)
                ->setTimezone('UTC');
        } else {
            $this->event_reminder_date = null;
        }

        // TODO(fabzo): This isn't currently reflected in the UI.
        if ($this->isEventReminderOutOfRange()) {
            $this->event_reminder_type = EventReminderType::NONE;
            $this->event_reminder_date = null;
        }

        if ($this->isValidRecurringParent() && $this->vote_reminder_type == EventVoteReminderType::HOURS_AFTER_CREATION) {
            $this->event_reminder_type = EventReminderType::NONE;
            $this->event_reminder_date = null;
        }

        return $this->event_reminder_date != $previousValue;
    }

    public function isVoteReminderOutOfRange(): bool {
        if ($this->vote_reminder_date === null) {
            return false;
        }

        $createdAt = $this->created_at ?: Carbon::now();

        // Sending the reminder before the event has been created, means that they would be sent immediately.
        $reminderBeforeEventCreation = $this->vote_reminder_date->isBefore($createdAt);

        // No point in sending the reminder if the event has already begun
        $reminderAfterEventBegin = $this->vote_reminder_date->isAfter($this->dateTimeBegin());

        return $reminderBeforeEventCreation || $reminderAfterEventBegin;
    }

    public function isEventReminderOutOfRange(): bool {
        if ($this->event_reminder_date === null) {
            return false;
        }

        $createdAt = $this->created_at ?: Carbon::now();

        // Sending the reminder before the event has been created, means that they would be sent immediately.
        $reminderBeforeEventCreation = $this->event_reminder_date->isBefore($createdAt);

        // No point in sending the reminder if the event has already begun
        $reminderAfterEventBegin = $this->event_reminder_date->isAfter($this->dateTimeBegin());

        return $reminderBeforeEventCreation || $reminderAfterEventBegin;
    }

    /**
     * @return HasMany<TeamEventVote, $this>
     */
    public function votes(): HasMany {
        return $this->hasMany(TeamEventVote::class)->whereHas('teamMember');
    }

    public function isValidRecurringParent(): bool {
        if($this->series_type === EventSeriesType::RECURRING_PARENT
            && $this->series instanceof TeamEventSeries
            && $this->series->getRRule() instanceof RRule) {
            // prevent lazy loading by setting relation directly, so already loaded relations are also available
            $this->series->setRelation('eventData', $this);
            return true;
        }
        return false;
    }

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * @return HasMany<TeamEventTask, $this>
     */
    public function tasks(): HasMany {
        return $this->hasMany(TeamEventTask::class);
    }

    /**
     * @return BelongsTo<TeamEventSeries, $this>
     */
    public function series(): BelongsTo {
        return $this->belongsTo(TeamEventSeries::class);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeEvents(Builder|TeamEvent $builder): void {
        $this->scopeNotSeriesParent($builder);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeSeriesParent(Builder|TeamEvent $builder): void {
        $builder->where('series_type', '=',EventSeriesType::RECURRING_PARENT);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeNotSeriesParent(Builder|TeamEvent $builder): void {
        $builder->where('series_type', '!=',EventSeriesType::RECURRING_PARENT);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeWithoutVoteReminderSent(Builder|TeamEvent $builder): void {
        $builder->where('vote_reminder_sent', false);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeVoteRemindersDue(Builder|TeamEvent $builder): void {
        $builder->where('vote_reminder_date', '<=', now())
            ->where('vote_reminder_date', '>=', now()->subDay());
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeEventRemindersDue(Builder|TeamEvent $builder): void {
        print " Checking for event reminders that are due. Current time: " . now() . "\n";
        $builder->where('event_reminder_date', '<=', now())
            ->where('event_reminder_date', '>=', now()->subDay());
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeWithoutEventReminderSent(Builder|TeamEvent $builder): void {
        $builder->where('event_reminder_sent', false);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scope(Builder|TeamEvent $builder): void {
        $builder->where('series_type', '=',EventSeriesType::RECURRING_PARENT);
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeNotCancelled(Builder|TeamEvent $builder): void {
        $builder->whereNull('cancelled_at');
    }

    /**
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeFuture(Builder|TeamEvent $builder): void {
        $builder->where('date_begin', '>=', Carbon::createMidnightDate());
    }

    /**
     * Scope a query to only include events that have not started yet.
     *
     * This differs from scopeFuture in that scopeFuture only compares
     * the date_begin (against midnight of the current day), whereas this scope
     * considers both date_begin and time_begin, ensuring that the event's
     * actual start datetime (as provided by dateTimeBegin()) is in the future.
     *
     * @param Builder<TeamEvent>|TeamEvent $builder
     */
    public function scopeNotStarted(Builder|TeamEvent $builder): void {
        // If time_begin is null, we assume the event starts at midnight.
        $builder->whereRaw(
            "TIMESTAMP(date_begin, IFNULL(time_begin, '00:00:00')) > ?",
            [now()]
        );
    }


    public function updateVotesAccordingToResponseType(): void {
        switch ($this->response_type) {
            case EventResponseType::AUTO_YES:
                foreach ($this->getMembersWhoHaveNotVoted() as $member) {
                    $this->createVote(
                        teamMember: $member,
                        vote: TeamEventVoteType::YES,
                    );
                }
                break;
            case EventResponseType::NONE:
            default:
                $this->deleteVotesForResponseTypeAutoYes();
                break;
        }
    }

    /**
     * @return Collection<int, TeamMember>
     */
    private function getMembersWhoHaveNotVoted(): Collection {
        $membersQuery = $this->team->activeMembers();
        $membersAlreadyVoted = $this->votes()->select('team_member_id');
        $membersQuery->whereNotIn('id', $membersAlreadyVoted);

        /** @var Collection<int, TeamMember> */
        return $membersQuery->get();
    }

    public function createVote(TeamMember $teamMember, TeamEventVoteType $vote, Person $voterPerson = null, Absence $absence = null, string $comment = null): TeamEventVote {
        $returnVote = $this->votes()->make([
            'vote' => $vote,
        ]);
        
        // Set relationships before saving to make them available during creating event of TeamEventVote
        $returnVote->teamMember()->associate($teamMember);
        $returnVote->voterPerson()->associate($voterPerson);
        $returnVote->absence()->associate($absence);
        $returnVote->save();
        
        if (isset($comment)) {
            $returnVote->setComment($comment, $voterPerson);
        }
        
        return $returnVote;
    }

    /**
     * deletes all votes without author
     *
     * @param TeamMember|null $member
     *
     * @return void
     */
    public function deleteVotesForResponseTypeAutoYes(TeamMember $member = null): void {
        $query = $this->votes()->withoutAuthor();
        if (isset($member)) {
            $query->whereTeamMemberId($member->id);
        }
        
        // If votes relation is loaded, get the IDs that will be deleted
        $idsToDelete = [];
        if ($this->relationLoaded('votes')) {
            $idsToDelete = $query->pluck('id')->toArray();
        }
        
        // Execute the delete query
        $query->delete();
        
        // Update the loaded relation by filtering out deleted votes
        if ($this->relationLoaded('votes') && !empty($idsToDelete)) {
            $this->setRelation('votes', $this->votes->reject(function ($vote) use ($idsToDelete) {
                return in_array($vote->id, $idsToDelete);
            }));
        }
    }

    public function checkVotesForAbsencesInRange(): void {
        if($this->isValidRecurringParent()){
            return;
        }
        $this->deleteVotesForAbsencesOutOfRange();
        $this->setVoteNoForAbsencesInRange();
    }

    private function deleteVotesForAbsencesOutOfRange(): void {
        $votesWithAbsences = $this->votes()->with('absence', fn($query) => $query->withoutGlobalScope(LimitToCurrentUserAbsencesScope::class))
            ->whereHas('absence', fn($query) => $query->withoutGlobalScope(LimitToCurrentUserAbsencesScope::class))
            ->get();
        foreach ($votesWithAbsences as $vote) {
            if (!$vote->absence->isEventWithinAbsenceRange($this)) {
                $vote->delete();
            }
        }
    }

    /**
     * @return void
     */
    public function setVoteNoForAbsencesInRange(): void {
        // select (matching recurring) absences in range for team members
        $absences = $this->getAbsencesInRangeForRelatedTeamMembers();

        // set votes
        foreach ($absences as $absence) {
            $members = $absence->getUniqueTargetTeamMembers($this->team);
            $absence->createNoVotesForEventAndMembers(teamEvent: $this, members: $members);
        }
    }

    /**
     * @return Collection<int, Absence>
     */
    private function getAbsencesInRangeForRelatedTeamMembers(): Collection {
        $absences = Absence::with('teamMembers', 'persons.teamMembers', 'author')
            ->withoutGlobalScope(LimitToCurrentUserAbsencesScope::class)
            ->whereDate('date_begin', '<=', $this->date_begin)
            ->where(function (Builder $query) {
                $query
                    ->whereNull('date_end')
                    ->orWhereDate('date_end', '>=', $this->date_begin);
            })
            ->where(function (Builder $query) {
                $query
                    ->whereRelation('teamMembers', 'team_id', $this->team_id)
                    ->orWhereRelation('persons.teamMembers', 'team_id', $this->team_id);
            })
            ->get();

        // filter absences based on their recurring rule
        return $absences->filter(function (Absence $absence) {
            return !$absence->isRecurring() ||
                $absence->getRRule()->occursAt($this->date_begin->toDateTime());
        });
    }   

public function getEventTitle(): string {
    return match ($this->event_type->value) {
        'match' => isset($this->match_type) 
            ? trans('events.game_type.' . $this->match_type->value) . ' ' . trans('events.against') . ' ' . $this->match_opponent_name
            : trans('events.game') . ' ' . trans('events.against') . ' ' . $this->match_opponent_name,
        default => $this->title ?? '',
    };
}
    
    public function dateTimeBegin(): Carbon {
        if ($this->time_begin) {
            return $this->date_begin->setTimeFrom($this->time_begin);
        }
        return $this->date_begin;
    }

    /**
     * this may be configured later depending on sport type or member age
     * @return int
     */
    private function getDefaultDurationInMinutes(): int {
        return 60;
    }
    
    public function dateTimeEnd(bool $addDefaultDurationIfNoEndTimeSet = false): Carbon {
        if ($this->time_end) {
            return $this->date_begin->setTimeFrom($this->time_end);
        }
        $dateTimeEnd = $this->dateTimeBegin()->clone();
        if($addDefaultDurationIfNoEndTimeSet) {
            $dateTimeEnd->addMinutes($this->getDefaultDurationInMinutes());
        }
        return $dateTimeEnd;
    }

    /**
     * @return Attribute<string|null, Carbon|null>
     */
    protected function timeBegin(): Attribute {
        return $this->getTimeAttributeAccessor();
    }

    /**
     * @return Attribute<string|null, Carbon|null>
     */
    private function getTimeAttributeAccessor(): Attribute {
        return Attribute::make(
            get: function(?string $value) {
                if (isset($value)) {
                    $formats = ['H:i:s', 'Y-m-d H:i:s', 'H:i'];
                    $lastException = null;
                    foreach ($formats as $format) {
                        try {
                            return Carbon::createFromFormat($format, $value);
                        } catch (\Exception $e) {
                            $lastException = $e;
                        }
                    }
                    throw $lastException;
                }
                return $value;
            },
        );
    }

    /**
     * @return Attribute<string|null, Carbon|null>
     */
    protected function timeEnd(): Attribute {
        return $this->getTimeAttributeAccessor();
    }

    /**
     * @return Attribute<string|null, Carbon|null>
     */
    protected function timeMeet(): Attribute {
        return $this->getTimeAttributeAccessor();
    }

    public function getPublicURL(): string {
        return config('app.frontend.url') . URL::route(
                'frontend.event.detail',
                $this->id,
                absolute: false,
            );
    }
}


